import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np




class BasicConv(nn.Module):

    def __init__(self, in_channels, out_channels, deconv=False, is_3d=False, bn=True, relu=True, **kwargs):
        super(BasicConv, self).__init__()

        self.relu = relu
        self.use_bn = bn
        if is_3d:
            if deconv:
                self.conv = nn.ConvTranspose3d(in_channels, out_channels, bias=False, **kwargs)
            else:
                self.conv = nn.Conv3d(in_channels, out_channels, bias=False, **kwargs)
            self.bn = nn.BatchNorm3d(out_channels)
        else:
            if deconv:
                self.conv = nn.ConvTranspose2d(in_channels, out_channels, bias=False, **kwargs)
            else:
                self.conv = nn.Conv2d(in_channels, out_channels, bias=False, **kwargs)
            self.bn = nn.BatchNorm2d(out_channels)

    def forward(self, x):
        x = self.conv(x)
        if self.use_bn:
            x = self.bn(x)
        if self.relu:
            x = nn.LeakyReLU()(x)#, inplace=True)
        return x


class Conv2x(nn.Module):

    def __init__(self, in_channels, out_channels, deconv=False, is_3d=False, concat=True, keep_concat=True, bn=True, relu=True, keep_dispc=False):
        super(Conv2x, self).__init__()
        self.concat = concat
        self.is_3d = is_3d 
        if deconv and is_3d: 
            kernel = (4, 4, 4)
        elif deconv:
            kernel = 4
        else:
            kernel = 3

        if deconv and is_3d and keep_dispc:
            kernel = (1, 4, 4)
            stride = (1, 2, 2)
            padding = (0, 1, 1)
            self.conv1 = BasicConv(in_channels, out_channels, deconv, is_3d, bn=True, relu=True, kernel_size=kernel, stride=stride, padding=padding)
        else:
            self.conv1 = BasicConv(in_channels, out_channels, deconv, is_3d, bn=True, relu=True, kernel_size=kernel, stride=2, padding=1)

        if self.concat: 
            mul = 2 if keep_concat else 1
            self.conv2 = BasicConv(out_channels*2, out_channels*mul, False, is_3d, bn, relu, kernel_size=3, stride=1, padding=1)
        else:

            self.conv2 = BasicConv(out_channels, out_channels, False, is_3d, bn, relu, kernel_size=3, stride=1, padding=1)
    def forward(self, x, rem):
        x = self.conv1(x)
        if x.shape != rem.shape:
            x = F.interpolate(
                x,
                size=(rem.shape[-2], rem.shape[-1]),
                mode='nearest')
        if self.concat:
            x = torch.cat((x, rem), 1)
        else: 
            x = x + rem
        x = self.conv2(x)
        return x


class BasicConv_IN(nn.Module):

    def __init__(self, in_channels, out_channels, deconv=False, is_3d=False, IN=True, relu=True, **kwargs):
        super(BasicConv_IN, self).__init__()

        self.relu = relu
        self.use_in = IN
        if is_3d:
            if deconv:
                self.conv = nn.ConvTranspose3d(in_channels, out_channels, bias=False, **kwargs)
            else:
                self.conv = nn.Conv3d(in_channels, out_channels, bias=False, **kwargs)
            self.IN = nn.InstanceNorm3d(out_channels)
        else:
            if deconv:
                self.conv = nn.ConvTranspose2d(in_channels, out_channels, bias=False, **kwargs)
            else:
                self.conv = nn.Conv2d(in_channels, out_channels, bias=False, **kwargs)
            self.IN = nn.InstanceNorm2d(out_channels)

    def forward(self, x):
        x = self.conv(x)
        if self.use_in:
            x = self.IN(x)
        if self.relu:
            x = nn.LeakyReLU()(x)#, inplace=True)
        return x


class Conv2x_IN(nn.Module):

    def __init__(self, in_channels, out_channels, deconv=False, is_3d=False, concat=True, keep_concat=True, IN=True, relu=True, keep_dispc=False):
        super(Conv2x_IN, self).__init__()
        self.concat = concat
        self.is_3d = is_3d 
        if deconv and is_3d: 
            kernel = (4, 4, 4)
        elif deconv:
            kernel = 4
        else:
            kernel = 3

        if deconv and is_3d and keep_dispc:
            kernel = (1, 4, 4)
            stride = (1, 2, 2)
            padding = (0, 1, 1)
            self.conv1 = BasicConv_IN(in_channels, out_channels, deconv, is_3d, IN=True, relu=True, kernel_size=kernel, stride=stride, padding=padding)
        else:
            self.conv1 = BasicConv_IN(in_channels, out_channels, deconv, is_3d, IN=True, relu=True, kernel_size=kernel, stride=2, padding=1)

        if self.concat: 
            mul = 2 if keep_concat else 1
            self.conv2 = BasicConv_IN(out_channels*2, out_channels*mul, False, is_3d, IN, relu, kernel_size=3, stride=1, padding=1)
        else:
            self.conv2 = BasicConv_IN(out_channels, out_channels, False, is_3d, IN, relu, kernel_size=3, stride=1, padding=1)

    def forward(self, x, rem):
        x = self.conv1(x)
        if x.shape != rem.shape:
            x = F.interpolate(
                x,
                size=(rem.shape[-2], rem.shape[-1]),
                mode='nearest')
        if self.concat:
            x = torch.cat((x, rem), 1)
        else: 
            x = x + rem
        x = self.conv2(x)
        return x


def groupwise_correlation(fea1, fea2, num_groups):
    B, C, H, W = fea1.shape
    assert C % num_groups == 0
    channels_per_group = C // num_groups
    cost = (fea1 * fea2).view([B, num_groups, channels_per_group, H, W]).mean(dim=2)
    assert cost.shape == (B, num_groups, H, W)
    return cost

def build_gwc_volume(refimg_fea, targetimg_fea, maxdisp, num_groups):
    B, C, H, W = refimg_fea.shape
    volume = refimg_fea.new_zeros([B, num_groups, maxdisp, H, W])
    for i in range(maxdisp):
        if i > 0:
            volume[:, :, i, :, i:] = groupwise_correlation(refimg_fea[:, :, :, i:], targetimg_fea[:, :, :, :-i],
                                                           num_groups)
        else:
            volume[:, :, i, :, :] = groupwise_correlation(refimg_fea, targetimg_fea, num_groups)
    volume = volume.contiguous()
    return volume
        



def norm_correlation(fea1, fea2):
    cost = torch.mean(((fea1/(torch.norm(fea1, 2, 1, True)+1e-05)) * (fea2/(torch.norm(fea2, 2, 1, True)+1e-05))), dim=1, keepdim=True)
    return cost

def build_norm_correlation_volume(refimg_fea, targetimg_fea, maxdisp):
    B, C, H, W = refimg_fea.shape
    volume = refimg_fea.new_zeros([B, 1, maxdisp, H, W])
    for i in range(maxdisp):
        if i > 0:
            volume[:, :, i, :, i:] = norm_correlation(refimg_fea[:, :, :, i:], targetimg_fea[:, :, :, :-i])
        else:
            volume[:, :, i, :, :] = norm_correlation(refimg_fea, targetimg_fea)
    volume = volume.contiguous()
    return volume

def correlation(fea1, fea2):
    cost = torch.sum((fea1 * fea2), dim=1, keepdim=True)
    return cost

def build_correlation_volume(refimg_fea, targetimg_fea, maxdisp):
    B, C, H, W = refimg_fea.shape
    volume = refimg_fea.new_zeros([B, 1, maxdisp, H, W])
    for i in range(maxdisp):
        if i > 0:
            volume[:, :, i, :, i:] = correlation(refimg_fea[:, :, :, i:], targetimg_fea[:, :, :, :-i])
        else:
            volume[:, :, i, :, :] = correlation(refimg_fea, targetimg_fea)
    volume = volume.contiguous()
    return volume



def build_concat_volume(refimg_fea, targetimg_fea, maxdisp):
    B, C, H, W = refimg_fea.shape
    volume = refimg_fea.new_zeros([B, 2 * C, maxdisp, H, W])
    for i in range(maxdisp):
        if i > 0:
            volume[:, :C, i, :, :] = refimg_fea[:, :, :, :]
            volume[:, C:, i, :, i:] = targetimg_fea[:, :, :, :-i]
        else:
            volume[:, :C, i, :, :] = refimg_fea
            volume[:, C:, i, :, :] = targetimg_fea
    volume = volume.contiguous()
    return volume

def disparity_regression(x, mindisp=0, maxdisp=192):
    assert len(x.shape) == 4
    num_disp = maxdisp - mindisp
    
    # 检查输入维度
    assert x.shape[1] == num_disp, f"Input has {x.shape[1]} channels, expected {num_disp} for disparity range [{mindisp}, {maxdisp})"
    
    disp_values = torch.arange(mindisp, maxdisp, dtype=x.dtype, device=x.device)
    disp_values = disp_values.view(1, num_disp, 1, 1)
    return torch.sum(x * disp_values, 1, keepdim=True)


class FeatureAtt(nn.Module):
    def __init__(self, cv_chan, feat_chan):
        super(FeatureAtt, self).__init__()

        self.feat_att = nn.Sequential(
            BasicConv(feat_chan, feat_chan//2, kernel_size=1, stride=1, padding=0),
            nn.Conv2d(feat_chan//2, cv_chan, 1))

    def forward(self, cv, feat):
        '''
        '''
        feat_att = self.feat_att(feat).unsqueeze(2)
        cv = torch.sigmoid(feat_att)*cv
        return cv

def context_upsample(disp_low, up_weights):
    ###
    # cv (b,1,h,w)
    # sp (b,9,4*h,4*w)
    ###
    b, c, h, w = disp_low.shape
        
    disp_unfold = F.unfold(disp_low.reshape(b,c,h,w),3,1,1).reshape(b,-1,h,w)
    disp_unfold = F.interpolate(disp_unfold,(h*4,w*4),mode='nearest').reshape(b,9,h*4,w*4)

    disp = (disp_unfold*up_weights).sum(1)
        
    return disp

class Propagation(nn.Module):
    def __init__(self):
        super(Propagation, self).__init__()
        self.replicationpad = nn.ReplicationPad2d(1)

    def forward(self, disparity_samples):

        one_hot_filter = torch.zeros(5, 1, 3, 3, device=disparity_samples.device).float()
        one_hot_filter[0, 0, 0, 0] = 1.0
        one_hot_filter[1, 0, 1, 1] = 1.0
        one_hot_filter[2, 0, 2, 2] = 1.0
        one_hot_filter[3, 0, 2, 0] = 1.0
        one_hot_filter[4, 0, 0, 2] = 1.0
        disparity_samples = self.replicationpad(disparity_samples)
        aggregated_disparity_samples = F.conv2d(disparity_samples,
                                                    one_hot_filter,padding=0)
                                                    
        return aggregated_disparity_samples
        

class Propagation_prob(nn.Module):
    def __init__(self):
        super(Propagation_prob, self).__init__()
        self.replicationpad = nn.ReplicationPad3d((1, 1, 1, 1, 0, 0))

    def forward(self, prob_volume):
        one_hot_filter = torch.zeros(5, 1, 1, 3, 3, device=prob_volume.device).float()
        one_hot_filter[0, 0, 0, 0, 0] = 1.0
        one_hot_filter[1, 0, 0, 1, 1] = 1.0
        one_hot_filter[2, 0, 0, 2, 2] = 1.0
        one_hot_filter[3, 0, 0, 2, 0] = 1.0
        one_hot_filter[4, 0, 0, 0, 2] = 1.0

        prob_volume = self.replicationpad(prob_volume)
        prob_volume_propa = F.conv3d(prob_volume, one_hot_filter,padding=0)


        return prob_volume_propa


# Monster Plus network components

class HourglassNetwork(nn.Module):
    """3D cost volume aggregation using hourglass architecture"""
    def __init__(self, in_channels):
        super(HourglassNetwork, self).__init__()

        # 编码器部分
        self.conv1 = nn.Sequential(
            BasicConv(in_channels, in_channels*2, is_3d=True, bn=True, relu=True, 
                     kernel_size=3, padding=1, stride=2, dilation=1),
            BasicConv(in_channels*2, in_channels*2, is_3d=True, bn=True, relu=True, 
                     kernel_size=3, padding=1, stride=1, dilation=1)
        )
                                    
        self.conv2 = nn.Sequential(
            BasicConv(in_channels*2, in_channels*4, is_3d=True, bn=True, relu=True, 
                     kernel_size=3, padding=1, stride=2, dilation=1),
            BasicConv(in_channels*4, in_channels*4, is_3d=True, bn=True, relu=True, 
                     kernel_size=3, padding=1, stride=1, dilation=1)
        )                             

        self.conv3 = nn.Sequential(
            BasicConv(in_channels*4, in_channels*6, is_3d=True, bn=True, relu=True, 
                     kernel_size=3, padding=1, stride=2, dilation=1),
            BasicConv(in_channels*6, in_channels*6, is_3d=True, bn=True, relu=True, 
                     kernel_size=3, padding=1, stride=1, dilation=1)
        ) 

        # 解码器部分
        self.conv3_up = BasicConv(in_channels*6, in_channels*4, deconv=True, is_3d=True, 
                                 bn=True, relu=True, kernel_size=(4, 4, 4), 
                                 padding=(1, 1, 1), stride=(2, 2, 2))

        self.conv2_up = BasicConv(in_channels*4, in_channels*2, deconv=True, is_3d=True, 
                                 bn=True, relu=True, kernel_size=(4, 4, 4), 
                                 padding=(1, 1, 1), stride=(2, 2, 2))

        self.conv1_up = BasicConv(in_channels*2, 8, deconv=True, is_3d=True, 
                                 bn=False, relu=False, kernel_size=(4, 4, 4), 
                                 padding=(1, 1, 1), stride=(2, 2, 2))

        # 聚合层
        self.agg_0 = nn.Sequential(
            BasicConv(in_channels*8, in_channels*4, is_3d=True, kernel_size=1, padding=0, stride=1),
            BasicConv(in_channels*4, in_channels*4, is_3d=True, kernel_size=3, padding=1, stride=1),
            BasicConv(in_channels*4, in_channels*4, is_3d=True, kernel_size=3, padding=1, stride=1)
        )

        self.agg_1 = nn.Sequential(
            BasicConv(in_channels*4, in_channels*2, is_3d=True, kernel_size=1, padding=0, stride=1),
            BasicConv(in_channels*2, in_channels*2, is_3d=True, kernel_size=3, padding=1, stride=1),
            BasicConv(in_channels*2, in_channels*2, is_3d=True, kernel_size=3, padding=1, stride=1)
        )

        # 特征注意力模块
        self.feature_att_8 = FeatureAtt(in_channels*2, 64)
        self.feature_att_16 = FeatureAtt(in_channels*4, 192)
        self.feature_att_32 = FeatureAtt(in_channels*6, 160)
        self.feature_att_up_16 = FeatureAtt(in_channels*4, 192)
        self.feature_att_up_8 = FeatureAtt(in_channels*2, 64)

    def forward(self, x, features):
        # 编码路径
        conv1 = self.conv1(x)
        conv1 = self.feature_att_8(conv1, features[1])

        conv2 = self.conv2(conv1)
        conv2 = self.feature_att_16(conv2, features[2])

        conv3 = self.conv3(conv2)
        conv3 = self.feature_att_32(conv3, features[3])

        # 解码路径
        conv3_up = self.conv3_up(conv3)
        conv2 = torch.cat((conv3_up, conv2), dim=1)
        conv2 = self.agg_0(conv2)
        conv2 = self.feature_att_up_16(conv2, features[2])

        conv2_up = self.conv2_up(conv2)
        conv1 = torch.cat((conv2_up, conv1), dim=1)
        conv1 = self.agg_1(conv1)
        conv1 = self.feature_att_up_8(conv1, features[1])

        conv = self.conv1_up(conv1)
        return conv


class FeatureTransfer(nn.Module):
    """Feature transfer network: DINOv2 features to stereo matching features"""
    def __init__(self, dim_list):
        super(FeatureTransfer, self).__init__()
        
        # 各尺度的特征转换层
        self.conv4x = nn.Sequential(
            nn.Conv2d(in_channels=int(48+dim_list[0]), out_channels=48, 
                     kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(48), nn.ReLU()
        )
        self.conv8x = nn.Sequential(
            nn.Conv2d(in_channels=int(64+dim_list[0]), out_channels=64, 
                     kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(64), nn.ReLU()
        )
        self.conv16x = nn.Sequential(
            nn.Conv2d(in_channels=int(192+dim_list[0]), out_channels=192, 
                     kernel_size=5, stride=1, padding=2),
            nn.InstanceNorm2d(192), nn.ReLU()
        )
        self.conv32x = nn.Sequential(
            nn.Conv2d(in_channels=dim_list[0], out_channels=160, 
                     kernel_size=3, stride=1, padding=1),
            nn.InstanceNorm2d(160), nn.ReLU()
        )
        
        # 上采样层
        self.conv_up_32x = nn.ConvTranspose2d(160, 192, kernel_size=3, padding=1, 
                                            output_padding=1, stride=2, bias=False)
        self.conv_up_16x = nn.ConvTranspose2d(192, 64, kernel_size=3, padding=1, 
                                            output_padding=1, stride=2, bias=False)
        self.conv_up_8x = nn.ConvTranspose2d(64, 48, kernel_size=3, padding=1, 
                                           output_padding=1, stride=2, bias=False)
    
        # 残差连接
        self.res_16x = nn.Conv2d(dim_list[0], 192, kernel_size=1, padding=0, stride=1)
        self.res_8x = nn.Conv2d(dim_list[0], 64, kernel_size=1, padding=0, stride=1)
        self.res_4x = nn.Conv2d(dim_list[0], 48, kernel_size=1, padding=0, stride=1)

    def forward(self, features):
        features_mono_list = []
        
        # 32x -> 16x
        feat_32x = self.conv32x(features[3])
        feat_32x_up = self.conv_up_32x(feat_32x)
        
        # 16x
        feat_16x = self.conv16x(torch.cat((features[2], feat_32x_up), 1)) + self.res_16x(features[2])
        feat_16x_up = self.conv_up_16x(feat_16x)
        
        # 8x
        feat_8x = self.conv8x(torch.cat((features[1], feat_16x_up), 1)) + self.res_8x(features[1])
        feat_8x_up = self.conv_up_8x(feat_8x)
        
        # 4x
        feat_4x = self.conv4x(torch.cat((features[0], feat_8x_up), 1)) + self.res_4x(features[0])
        
        features_mono_list.extend([feat_4x, feat_8x, feat_16x, feat_32x])
        return features_mono_list


class ContextFeatureTransfer(nn.Module):
    """Context network feature transfer for GRU context preparation"""
    def __init__(self, dim_list, output_dim):
        super(ContextFeatureTransfer, self).__init__()
        
        self.res_16x = nn.Conv2d(dim_list[0]+192, output_dim, kernel_size=3, padding=1, stride=1)
        self.res_8x = nn.Conv2d(dim_list[0]+96, output_dim, kernel_size=3, padding=1, stride=1)
        self.res_4x = nn.Conv2d(dim_list[0]+48, output_dim, kernel_size=3, padding=1, stride=1)

    def forward(self, features, stem_x_list):
        """Process context features for multiple scales"""
        feat_16x = self.res_16x(torch.cat((features[2], stem_x_list[0]), 1))
        feat_8x = self.res_8x(torch.cat((features[1], stem_x_list[1]), 1))
        feat_4x = self.res_4x(torch.cat((features[0], stem_x_list[2]), 1))
        return [feat_4x, feat_8x, feat_16x]
    



def efficient_local_similarity(feature_map, kernel_size=9):
    """
    高效计算每个像素与其邻域的相似度图。

    Args:
        feature_map (torch.Tensor): 输入的特征图，形状为 [B, C, H, W]。
        kernel_size (int): 邻域大小。

    Returns:
        dict: 包含不同相似度度量的字典。
              'cosine': 余弦相似度图, [B, K*K, H, W]
    """
    b, c, h, w = feature_map.shape
    k = kernel_size
    k2 = k * k

    # --- 1. 使用 Unfold 提取邻域块 ---
    # padding是为了让输出的H, W与输入保持一致
    padding = (k - 1) // 2
    unfolder = nn.Unfold(kernel_size=(k, k), padding=padding)
    
    # unfolded_features 的形状: [B, C * K*K, H*W]
    unfolded_features = unfolder(feature_map)
    
    # 变形为更直观的邻域表示: [B, C, K*K, H, W]
    # 这是所有像素的邻居们的特征
    neighbors_feat = unfolded_features.view(b, c, k2, h, w)

    # --- 2. 准备中心像素的特征 ---
    # 为了进行广播计算，需要将中心像素特征扩展一个维度
    # [B, C, H, W] -> [B, C, 1, H, W]
    center_feat = feature_map.unsqueeze(2)

    # --- 3. 并行计算相似度 ---
    
    # 3.1 计算点积 (Dot Product)
    # 使用广播机制，(B, C, 1, H, W) * (B, C, K*K, H, W) -> (B, C, K*K, H, W)
    # 然后在通道维度C上求和
    dot_product = torch.sum(center_feat * neighbors_feat, dim=1)
    # dot_product 形状: [B, K*K, H, W]

    # 3.2 计算范数 (Norms)
    center_norm = torch.linalg.norm(center_feat, dim=1) # [B, 1, H, W]
    neighbors_norm = torch.linalg.norm(neighbors_feat, dim=1) # [B, K*K, H, W]
    
    # 3.3 计算余弦相似度
    # 加上一个很小的eps防止除以零
    epsilon = 1e-8
    cosine_similarity = dot_product / (center_norm * neighbors_norm + epsilon)
    
    return cosine_similarity



def chunked_by_neighbor_similarity(feature_map: torch.Tensor, kernel_size: int = 9) -> torch.Tensor:
    """
    通过逐个处理邻居来分块计算局部相似度，以降低峰值显存。

    Args:
        feature_map (torch.Tensor): 输入的特征图，形状为 [B, C, H, W]。
        kernel_size (int): 邻域大小。

    Returns:
        torch.Tensor: 余弦相似度图, [B, K*K, H, W]。
    """
    b, c, h, w = feature_map.shape
    k = kernel_size
    k2 = k * k
    padding = (k - 1) // 2
    epsilon = 1e-8

    # --- 1. 准备中心像素的特征和范数 ---
    # 这部分计算不消耗太多内存，可以提前完成
    center_feat = feature_map
    center_norm = torch.linalg.norm(center_feat, dim=1, keepdim=True) # [B, 1, H, W]

    # --- 2. 预先对整个特征图进行一次性padding ---
    # 避免在循环中反复padding
    padded_feature_map = F.pad(feature_map, (padding, padding, padding, padding))

    # --- 3. 准备一个空的输出张量 ---
    # 我们将逐个填充这个张量的 K*K 通道
    cosine_similarity = torch.empty(b, k2, h, w, device=feature_map.device, dtype=feature_map.dtype)

    # --- 4. 逐个邻居进行循环计算 ---
    neighbor_idx = 0
    # dy, dx 是邻居相对于中心像素的偏移量
    for dy in range(k):
        for dx in range(k):
            # --- a. 从 padded_feature_map 中提取当前邻居的特征图 ---
            # 通过偏移和裁剪，得到与原始feature_map对齐的邻居特征图
            # 例如，对于左上角邻居(dy=0, dx=0)，我们从(0,0)开始切片
            # 对于中心邻居(dy=padding, dx=padding)，我们从(padding, padding)开始切片，等同于原图
            h_start, w_start = dy, dx
            neighbor_feat = padded_feature_map[:, :, h_start:h_start+h, w_start:w_start+w]
            
            # --- b. 计算与这个邻居的相似度 ---
            # 点积
            dot_product = torch.sum(center_feat * neighbor_feat, dim=1) # [B, H, W]
            
            # 邻居范数
            neighbors_norm = torch.linalg.norm(neighbor_feat, dim=1, keepdim=True) # [B, 1, H, W]
            
            # 余弦相似度
            sim = dot_product / (center_norm.squeeze(1) * neighbors_norm.squeeze(1) + epsilon) # [B, H, W]
            
            # --- c. 将结果存入输出张量的对应位置 ---
            cosine_similarity[:, neighbor_idx, :, :] = sim
            
            neighbor_idx += 1
            
    return cosine_similarity