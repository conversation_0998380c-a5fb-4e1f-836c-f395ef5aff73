#!/usr/bin/env python3
"""
简单测试VGGT的多尺度特征生成
"""

import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'vggt'))

from vggt.models.vggt import VGGT

def test_vggt_simple():
    """简单测试VGGT"""
    print("🚀 简单测试VGGT多尺度特征生成...")
    
    model = VGGT(img_size=512, patch_size=14, embed_dim=1024).cuda()
    model.eval()
    
    # 测试不同输入尺寸
    test_sizes = [(256, 256), (512, 512), (768, 768), (2048, 4096)]
    
    for h, w in test_sizes:
        print(f"\n📏 测试输入尺寸: {h}x{w}")
        
        images = torch.randn(1, 2, 3, h, w)
        
        with torch.no_grad():
            predictions = model(images.cuda())
        
        print(f"  输出特征图:")
        for feat_name in ["feat_1_4", "feat_1_8", "feat_1_16", "feat_1_32"]:
            if feat_name in predictions:
                feat = predictions[feat_name]
                feat_h, feat_w = feat.shape[-2], feat.shape[-1]
                
                # 计算期望尺寸
                scale = int(feat_name.split('_')[2])  # 修复：应该是split('_')[2]，因为feat_1_4中的4是第3个元素
                expected_h, expected_w = h // scale, w // scale

                # 调试信息
                print(f"    DEBUG: feat_name={feat_name}, scale={scale}, h={h}, w={w}")
                print(f"    DEBUG: expected_h={expected_h}, expected_w={expected_w}")
                print(f"    DEBUG: feat_h={feat_h}, feat_w={feat_w}")

                # 检查是否匹配
                match = feat_h == expected_h and feat_w == expected_w
                match_str = "✅" if match else "❌"

                print(f"    {feat_name}: {feat_h}x{feat_w} (期望: {expected_h}x{expected_w}) {match_str}")

                if not match:
                    print(f"      ⚠️  尺寸不匹配! 实际比例: 1/{h//feat_h}")

if __name__ == "__main__":
    test_vggt_simple()
