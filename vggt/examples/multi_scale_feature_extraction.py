"""
Multi-Scale Feature Extraction Example
=====================================

This example demonstrates how to use the new MultiScaleDPTHead to extract 
multi-scale feature pyramids from VGGT tokens for stereo matching applications.

The MultiScaleDPTHead extracts features at 4 different scales:
- 1/4 resolution: [B, S, 96, H/4, W/4]
- 1/8 resolution: [B, S, 192, H/8, W/8]  
- 1/16 resolution: [B, S, 384, H/16, W/16]
- 1/32 resolution: [B, S, 768, H/32, W/32]

This replaces the original depth/confidence output with multi-scale features
suitable for downstream stereo matching tasks.
"""

import torch
import torch.nn as nn
from vggt.vggt.heads import MultiScaleDPTHead
from vggt.vggt.models.aggregator import Aggregator


class VGGTStereoFeatureExtractor(nn.Module):
    """
    Example wrapper that combines VGGT aggregator with MultiScaleDPTHead
    for stereo matching feature extraction.
    """
    
    def __init__(
        self,
        # VGGT Aggregator parameters
        img_size=518,
        patch_size=14,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        # MultiScaleDPTHead parameters
        out_channels=[96, 192, 384, 768],  # Feature channels for each scale
        intermediate_layer_idx=[4, 11, 17, 23],  # Which VGGT layers to use
    ):
        super().__init__()
        
        # VGGT Aggregator for processing stereo pairs
        self.aggregator = Aggregator(
            img_size=img_size,
            patch_size=patch_size,
            embed_dim=embed_dim,
            depth=depth,
            num_heads=num_heads,
            aa_order=["frame", "global"],  # Alternating attention
            aa_block_size=1,
        )
        
        # Multi-scale feature extraction head
        self.feature_head = MultiScaleDPTHead(
            dim_in=embed_dim * 2,  # VGGT outputs 2*embed_dim (frame + global)
            patch_size=patch_size,
            out_channels=out_channels,
            intermediate_layer_idx=intermediate_layer_idx,
            pos_embed=True,
        )
    
    def forward(self, stereo_images):
        """
        Extract multi-scale features from stereo image pairs.
        
        Args:
            stereo_images: [B, 2, 3, H, W] - Batch of stereo pairs (left, right)
        
        Returns:
            List[torch.Tensor]: Multi-scale features
                - feat_1_4: [B, 2, 96, H/4, W/4]
                - feat_1_8: [B, 2, 192, H/8, W/8]
                - feat_1_16: [B, 2, 384, H/16, W/16]
                - feat_1_32: [B, 2, 768, H/32, W/32]
        """
        # Process through VGGT aggregator
        aggregated_tokens_list, patch_start_idx = self.aggregator(stereo_images)
        
        # Extract multi-scale features
        multi_scale_features = self.feature_head(
            aggregated_tokens_list=aggregated_tokens_list,
            images=stereo_images,
            patch_start_idx=patch_start_idx,
        )
        
        return multi_scale_features


def example_usage():
    """
    Example of how to use the VGGTStereoFeatureExtractor.
    """
    # Create model
    model = VGGTStereoFeatureExtractor(
        img_size=518,
        patch_size=14,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        out_channels=[96, 192, 384, 768],
        intermediate_layer_idx=[4, 11, 17, 23],
    )
    
    # Example input: batch of stereo pairs
    B, H, W = 2, 518, 518
    stereo_images = torch.randn(B, 2, 3, H, W)  # [batch, left/right, RGB, height, width]
    
    # Extract multi-scale features
    with torch.no_grad():
        multi_scale_features = model(stereo_images)
    
    # Print feature shapes
    scale_names = ["1/4", "1/8", "1/16", "1/32"]
    for i, (features, scale_name) in enumerate(zip(multi_scale_features, scale_names)):
        print(f"Scale {scale_name}: {features.shape}")
    
    return multi_scale_features


def integration_example():
    """
    Example showing how to integrate with existing VGGT model code.
    
    This shows how to replace the original depth head with the multi-scale head.
    """
    
    # Original code (commented out):
    # depth, depth_conf = self.depth_head(
    #     aggregated_tokens_list, images, patch_start_idx
    # )
    
    # New code for multi-scale feature extraction:
    
    # Initialize the multi-scale head (this would be done in __init__)
    multi_scale_head = MultiScaleDPTHead(
        dim_in=1024 * 2,  # VGGT embed_dim * 2
        patch_size=14,
        out_channels=[96, 192, 384, 768],  # Monster Plus vitb config
        intermediate_layer_idx=[4, 11, 17, 23],
        pos_embed=True,
    )
    
    # Example inputs (these would come from your existing VGGT pipeline)
    B, S, H, W = 1, 2, 518, 518  # Batch=1, Stereo pair=2, Height=518, Width=518
    images = torch.randn(B, S, 3, H, W)
    
    # Mock aggregated tokens (this would come from self.aggregator)
    embed_dim = 1024
    patch_h, patch_w = H // 14, W // 14
    num_patches = patch_h * patch_w
    patch_start_idx = 5  # Example: 1 camera + 4 register tokens
    total_tokens = patch_start_idx + num_patches
    
    aggregated_tokens_list = [
        torch.randn(B, S * total_tokens, embed_dim * 2)  # [B, S*P, 2*C]
        for _ in range(24)  # 24 layers
    ]
    
    # Extract multi-scale features
    multi_scale_features = multi_scale_head(
        aggregated_tokens_list=aggregated_tokens_list,
        images=images,
        patch_start_idx=patch_start_idx,
    )
    
    # Now you have multi-scale features instead of depth/confidence
    feat_1_4, feat_1_8, feat_1_16, feat_1_32 = multi_scale_features
    
    print("Multi-scale features extracted:")
    print(f"1/4 scale: {feat_1_4.shape}")    # [1, 2, 96, 129, 129]
    print(f"1/8 scale: {feat_1_8.shape}")    # [1, 2, 192, 64, 64]  
    print(f"1/16 scale: {feat_1_16.shape}")  # [1, 2, 384, 32, 32]
    print(f"1/32 scale: {feat_1_32.shape}")  # [1, 2, 768, 16, 16]
    
    return multi_scale_features


if __name__ == "__main__":
    print("=== Multi-Scale Feature Extraction Example ===")
    
    print("\n1. Basic usage example:")
    example_usage()
    
    print("\n2. Integration example:")
    integration_example()
    
    print("\nMulti-scale feature extraction completed successfully!")
