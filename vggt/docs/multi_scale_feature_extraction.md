# Multi-Scale Feature Extraction for VGGT

## Overview

This document describes the new `MultiScaleDPTHead` implementation that extracts multi-scale feature pyramids from VGGT tokens for stereo matching applications, replacing the original depth/confidence output.

## Key Features

### 1. **Multi-Scale Output**
- **1/4 Resolution**: `[B, S, 96, H/4, W/4]` - Highest resolution features
- **1/8 Resolution**: `[B, S, 192, H/8, W/8]` - Medium-high resolution  
- **1/16 Resolution**: `[B, S, 384, H/16, W/16]` - Medium resolution
- **1/32 Resolution**: `[B, S, 768, H/32, W/32]` - Lowest resolution

### 2. **Compatibility**
- **Input**: Uses existing `aggregated_tokens_list` and `patch_start_idx` from VGGT aggregator
- **Interface**: Maintains similar API to original `DPTHead`
- **Architecture**: Compatible with VGGT token format and processing pipeline

### 3. **Efficiency**
- **Memory Management**: Supports frame chunking for large sequences
- **Progressive Downsampling**: Efficient pyramid construction through sequential downsampling
- **Positional Embedding**: Optional positional encoding for enhanced spatial awareness

## Architecture Details

### Core Components

1. **Projection Layers**: Convert VGGT tokens to target feature dimensions
2. **Scale Processors**: Refine features at each resolution level
3. **Downsampling Layers**: Create pyramid through progressive downsampling
4. **Positional Embedding**: Add spatial awareness to features

### Processing Pipeline

```
VGGT Tokens [B*S, P, 2*C]
    ↓
Normalize & Reshape [B*S, 2*C, patch_h, patch_w]
    ↓
Project to Base Features [B*S, out_channels[0], patch_h, patch_w]
    ↓
Upsample to 1/4 Resolution [B*S, out_channels[0], H/4, W/4]
    ↓
Progressive Downsampling & Processing
    ├── 1/4 Scale: [B, S, 96, H/4, W/4]
    ├── 1/8 Scale: [B, S, 192, H/8, W/8]
    ├── 1/16 Scale: [B, S, 384, H/16, W/16]
    └── 1/32 Scale: [B, S, 768, H/32, W/32]
```

## Usage

### Basic Integration

Replace the original depth head usage:

```python
# Original code
depth, depth_conf = self.depth_head(
    aggregated_tokens_list, images, patch_start_idx
)

# New multi-scale feature extraction
multi_scale_features = self.feature_head(
    aggregated_tokens_list, images, patch_start_idx
)
feat_1_4, feat_1_8, feat_1_16, feat_1_32 = multi_scale_features
```

### Initialization

```python
from vggt.vggt.heads import MultiScaleDPTHead

# Initialize multi-scale head
self.feature_head = MultiScaleDPTHead(
    dim_in=embed_dim * 2,  # VGGT outputs 2*embed_dim
    patch_size=14,
    out_channels=[96, 192, 384, 768],  # Feature channels for each scale
    intermediate_layer_idx=[4, 11, 17, 23],  # Which VGGT layers to use
    pos_embed=True,
)
```

## Configuration Options

### Output Channels
- **Default**: `[96, 192, 384, 768]` (Monster Plus vitb configuration)
- **Custom**: Adjust based on downstream task requirements
- **Scaling**: Higher channels for more complex features

### Intermediate Layers
- **Default**: `[4, 11, 17, 23]` (4 layers from 24-layer VGGT)
- **Selection**: Choose layers with appropriate semantic levels
- **Spacing**: Distribute across network depth for diverse features

### Positional Embedding
- **Enabled**: Better spatial awareness for stereo matching
- **Ratio**: Default 0.1, adjustable based on task requirements
- **Optional**: Can be disabled for pure feature extraction

## Performance Considerations

### Memory Usage
- **Frame Chunking**: Process large sequences in chunks
- **Progressive Processing**: Efficient pyramid construction
- **Gradient Checkpointing**: Optional for memory-constrained scenarios

### Computational Efficiency
- **Single Pass**: Extract all scales in one forward pass
- **Shared Base**: Common processing for efficiency
- **Optimized Downsampling**: Efficient scale transitions

## Comparison with Original DPTHead

| Aspect | Original DPTHead | MultiScaleDPTHead |
|--------|------------------|-------------------|
| **Output** | Depth + Confidence | Multi-scale Features |
| **Scales** | Single resolution | 4 resolutions (1/4 to 1/32) |
| **Channels** | 1 + 1 | 96 + 192 + 384 + 768 |
| **Use Case** | Depth estimation | Stereo matching features |
| **Processing** | Complex fusion | Progressive downsampling |
| **Memory** | Lower | Higher (more features) |

## Integration with Stereo Matching

The extracted multi-scale features are designed to be compatible with stereo matching pipelines:

1. **Feature Transfer**: Direct use in stereo matching networks
2. **Scale Matching**: Different scales for different matching stages
3. **Context Preservation**: Rich multi-scale context for accurate matching
4. **Efficiency**: Optimized for real-time stereo processing

## Example Applications

- **Stereo Depth Estimation**: Use features for cost volume construction
- **Optical Flow**: Multi-scale features for motion estimation  
- **3D Scene Understanding**: Rich features for geometric reasoning
- **SLAM**: Visual odometry and mapping applications

## Future Extensions

- **Adaptive Scaling**: Dynamic scale selection based on scene content
- **Cross-Scale Attention**: Enhanced interaction between scales
- **Task-Specific Heads**: Specialized processing for different applications
- **Efficiency Optimizations**: Further memory and compute optimizations
