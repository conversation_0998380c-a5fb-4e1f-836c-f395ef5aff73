# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union, List, Dict, Any

from vggt.layers import PatchEmbed
from vggt.layers.block import Block
from vggt.layers.rope import RotaryPositionEmbedding2D, PositionGetter
from vggt.layers.vision_transformer import vit_small, vit_base, vit_large, vit_giant2

logger = logging.getLogger(__name__)

_RESNET_MEAN = [0.485, 0.456, 0.406]
_RESNET_STD = [0.229, 0.224, 0.225]


class Aggregator(nn.Module):
    """
    The Aggregator applies alternating-attention over input frames,
    as described in VGGT: Visual Geometry Grounded Transformer.


    Args:
        img_size (int): Image size in pixels.
        patch_size (int): Size of each patch for PatchEmbed.
        embed_dim (int): Dimension of the token embeddings.
        depth (int): Number of blocks.
        num_heads (int): Number of attention heads.
        mlp_ratio (float): Ratio of MLP hidden dim to embedding dim.
        num_register_tokens (int): Number of register tokens.
        block_fn (nn.Module): The block type used for attention (Block by default).
        qkv_bias (bool): Whether to include bias in QKV projections.
        proj_bias (bool): Whether to include bias in the output projection.
        ffn_bias (bool): Whether to include bias in MLP layers.
        patch_embed (str): Type of patch embed. e.g., "conv" or "dinov2_vitl14_reg".
        aa_order (list[str]): The order of alternating attention, e.g. ["frame", "global"].
        aa_block_size (int): How many blocks to group under each attention type before switching. If not necessary, set to 1.
        qk_norm (bool): Whether to apply QK normalization.
        rope_freq (int): Base frequency for rotary embedding. -1 to disable.
        init_values (float): Init scale for layer scale.
    """

    def __init__(
        self,
        img_size=518,
        patch_size=14,
        embed_dim=1024,
        depth=24,
        num_heads=16,
        mlp_ratio=4.0,
        num_register_tokens=4,
        block_fn=Block,
        qkv_bias=True,
        proj_bias=True,
        ffn_bias=True,
        patch_embed="dinov2_vitl14_reg",
        aa_order=["frame", "global"],
        aa_block_size=1,
        qk_norm=True,
        rope_freq=100,
        init_values=0.01,
        intermediate_layer_idx: List[int] = [4, 11, 17, 23],
    ):
        super().__init__()

        self.__build_patch_embed__(patch_embed, img_size, patch_size, num_register_tokens, embed_dim=embed_dim)

        # Initialize rotary position embedding if frequency > 0
        self.rope = RotaryPositionEmbedding2D(frequency=rope_freq) if rope_freq > 0 else None
        self.position_getter = PositionGetter() if self.rope is not None else None

        self.frame_blocks = nn.ModuleList(
            [
                block_fn(
                    dim=embed_dim,
                    num_heads=num_heads,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    proj_bias=proj_bias,
                    ffn_bias=ffn_bias,
                    init_values=init_values,
                    qk_norm=qk_norm,
                    rope=self.rope,
                )
                for _ in range(depth)
            ]
        )

        self.global_blocks = nn.ModuleList(
            [
                block_fn(
                    dim=embed_dim,
                    num_heads=num_heads,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    proj_bias=proj_bias,
                    ffn_bias=ffn_bias,
                    init_values=init_values,
                    qk_norm=qk_norm,
                    rope=self.rope,
                )
                for _ in range(depth)
            ]
        )

        self.depth = depth
        self.aa_order = aa_order
        self.patch_size = patch_size
        self.aa_block_size = aa_block_size

        # Validate that depth is divisible by aa_block_size
        if self.depth % self.aa_block_size != 0:
            raise ValueError(f"depth ({depth}) must be divisible by aa_block_size ({aa_block_size})")

        self.aa_block_num = self.depth // self.aa_block_size
        self.intermediate_layer_idx = intermediate_layer_idx

        # Note: We have two camera tokens, one for the first frame and one for the rest
        # The same applies for register tokens
        # camera_token: [1, 2, 1, embed_dim]
        self.camera_token = nn.Parameter(torch.randn(1, 2, 1, embed_dim))
        # register_token: [1, 2, num_register_tokens, embed_dim]
        self.register_token = nn.Parameter(torch.randn(1, 2, num_register_tokens, embed_dim))

        # The patch tokens start after the camera and register tokens
        self.patch_start_idx = 1 + num_register_tokens

        # Initialize parameters with small values
        nn.init.normal_(self.camera_token, std=1e-6)
        nn.init.normal_(self.register_token, std=1e-6)

        # Register normalization constants as buffers
        for name, value in (
            ("_resnet_mean", _RESNET_MEAN),
            ("_resnet_std", _RESNET_STD),
        ):
            self.register_buffer(
                name,
                torch.FloatTensor(value).view(1, 1, 3, 1, 1),
                persistent=False,
            )

    def __build_patch_embed__(
        self,
        patch_embed,
        img_size,
        patch_size,
        num_register_tokens,
        interpolate_antialias=True,
        interpolate_offset=0.0,
        block_chunks=0,
        init_values=1.0,
        embed_dim=1024,
    ):
        """
        Build the patch embed layer. If 'conv', we use a
        simple PatchEmbed conv layer. Otherwise, we use a vision transformer.
        """

        if "conv" in patch_embed:
            self.patch_embed = PatchEmbed(img_size=img_size, patch_size=patch_size, in_chans=3, embed_dim=embed_dim)
        else:
            vit_models = {
                "dinov2_vitl14_reg": vit_large,
                "dinov2_vitb14_reg": vit_base,
                "dinov2_vits14_reg": vit_small,
                "dinov2_vitg2_reg": vit_giant2,
            }

            self.patch_embed = vit_models[patch_embed](
                img_size=img_size,
                patch_size=patch_size,
                num_register_tokens=num_register_tokens,
                interpolate_antialias=interpolate_antialias,
                interpolate_offset=interpolate_offset,
                block_chunks=block_chunks,
                init_values=init_values,
            )

            # Disable gradient updates for mask token
            if hasattr(self.patch_embed, "mask_token"):
                self.patch_embed.mask_token.requires_grad_(False)

    def forward(
        self,
        images: torch.Tensor,
    ) -> Tuple[List[torch.Tensor], int]:
        """
        Args:
            images (torch.Tensor): Input images with shape [B, S, 3, H, W], in range [0, 1].
                B: batch size, S: sequence length, 3: RGB channels, H: height, W: width

        Returns:
            (list[torch.Tensor], int):
                The list of outputs from the attention blocks,
                and the patch_start_idx indicating where patch tokens begin.
                
                Each element in the output list has shape [B, S, P, 2*C]
                where P is the number of tokens and C is the embedding dimension.
        """
        # images: [B, S, 3, H, W]
        B, S, C_in, H, W = images.shape

        if C_in != 3:
            raise ValueError(f"Expected 3 input channels, got {C_in}")

        # Normalize images and reshape for patch embed
        # images: [B, S, 3, H, W]
        images = (images - self._resnet_mean) / self._resnet_std

        # Reshape to [B*S, C, H, W] for patch embedding
        # images: [B*S, 3, H, W]
        images = images.view(B * S, C_in, H, W)
        # patch_tokens: [B*S, P - patch_start_idx, C] 其中P是总token数
        patch_tokens = self.patch_embed(images)

        if isinstance(patch_tokens, dict):
            patch_tokens = patch_tokens["x_norm_patchtokens"]

        # patch_tokens: [B*S, P - patch_start_idx, C]
        _, P_minus_start, C = patch_tokens.shape

        # Expand camera and register tokens to match batch size and sequence length
        # camera_token: [B*S, 1, C]
        camera_token = slice_expand_and_flatten(self.camera_token, B, S)
        # register_token: [B*S, num_register_tokens, C]
        register_token = slice_expand_and_flatten(self.register_token, B, S)

        # Concatenate special tokens with patch tokens
        # tokens: [B*S, P, C] 其中P = 1 + num_register_tokens + (P - patch_start_idx)
        tokens = torch.cat([camera_token, register_token, patch_tokens], dim=1)

        pos = None
        if self.rope is not None:
            # pos: [B*S, H//patch_size * W//patch_size, 2] = [B*S, P - patch_start_idx, 2]
            pos = self.position_getter(B * S, H // self.patch_size, W // self.patch_size, device=images.device)

        if self.patch_start_idx > 0:
            # do not use position embedding for special tokens (camera and register tokens)
            # so set pos to 0 for the special tokens
            pos = pos + 1
            # pos_special: [B*S, patch_start_idx, 2]
            pos_special = torch.zeros(B * S, self.patch_start_idx, 2).to(images.device).to(pos.dtype)
            # pos: [B*S, P, 2]
            pos = torch.cat([pos_special, pos], dim=1)

        # update P because we added special tokens
        # tokens: [B*S, P, C]
        _, P, C = tokens.shape

        frame_idx = 0
        global_idx = 0
        output_list = []

        # 处理交替注意力块
        for _ in range(self.aa_block_num):
            for attn_type in self.aa_order:
                if attn_type == "frame":
                    # 处理帧内注意力
                    tokens, frame_idx, frame_intermediates = self._process_frame_attention(
                        tokens, B, S, P, C, frame_idx, pos=pos
                    )
                elif attn_type == "global":
                    # 处理全局注意力（立体匹配中的条带注意力）
                    tokens, global_idx, global_intermediates = self._process_global_attention(
                        tokens, B, S, P, C, global_idx, H, W, pos=pos
                    )
                else:
                    raise ValueError(f"Unknown attention type: {attn_type}")

            # 将帧注意力和全局注意力的中间结果连接
            # frame_intermediates[i]: [B, S, P, C]
            # global_intermediates[i]: [B, S, P, C]
            # concat_inter: [B, S, P, 2*C]
            for i in range(len(frame_intermediates)):
                # Check if this layer should be included in the output
                current_layer_idx = frame_idx - len(frame_intermediates) + i
                if current_layer_idx in self.intermediate_layer_idx:
                    # concat frame and global intermediates, [B x S x P x 2C]
                    concat_inter = torch.cat([frame_intermediates[i], global_intermediates[i]], dim=-1)
                    output_list.append(concat_inter)

        del concat_inter
        del frame_intermediates
        del global_intermediates
        # output_list: List[torch.Tensor]，每个元素形状为 [B, S, P, 2*C]
        return output_list, self.patch_start_idx

    def _process_frame_attention(self, tokens, B, S, P, C, frame_idx, pos=None):
        """
        Process frame attention blocks. We keep tokens in shape (B*S, P, C).
        
        Args:
            tokens: 输入tokens [B*S, P, C]
            B: 批处理大小
            S: 序列长度
            P: token数量
            C: 通道维度
            frame_idx: 当前帧块索引
            pos: 位置嵌入
            
        Returns:
            tokens: 处理后的tokens [B*S, P, C]
            frame_idx: 更新后的帧块索引
            intermediates: 中间结果列表，每个元素形状为 [B, S, P, C]
        """
        # If needed, reshape tokens or positions:
        # 确保tokens形状为 [B*S, P, C]
        if tokens.shape != (B * S, P, C):
            tokens = tokens.view(B, S, P, C).view(B * S, P, C)

        # 确保pos形状为 [B*S, P, 2]
        if pos is not None and pos.shape != (B * S, P, 2):
            pos = pos.view(B, S, P, 2).view(B * S, P, 2)

        intermediates = []

        # by default, self.aa_block_size=1, which processes one block at a time
        for _ in range(self.aa_block_size):
            # tokens: [B*S, P, C] -> [B*S, P, C]
            tokens = self.frame_blocks[frame_idx](tokens, pos=pos)
            frame_idx += 1
            # 中间结果形状: [B, S, P, C]
            intermediates.append(tokens.view(B, S, P, C))

        return tokens, frame_idx, intermediates

    # def _process_global_attention(self, tokens, B, S, P, C, global_idx, H, W, pos=None):
    #     """
    #     处理基于条带的注意力机制，专门用于立体匹配任务。

    #     对于立体匹配，我们实现基于条带的注意力机制，其中：
    #     - 图像被划分为高度为4像素的水平条带
    #     - 在左右视图的对应条带之间计算交叉注意力
    #     - 这利用了对极约束，即匹配点位于相同的水平线上

    #     参数:
    #         tokens: 输入tokens [B*S, P, C] 或 [B, S*P, C]
    #         B: 批处理大小
    #         S: 序列长度（对于立体对应该是2：左视图和右视图）
    #         P: 每张图像的patch数
    #         C: 通道维度
    #         global_idx: 当前全局块索引
    #         H: 图像高度
    #         W: 图像宽度
    #         pos: 位置嵌入
            
    #     Returns:
    #         tokens: 处理后的tokens [B*S, P, C] 或 [B, S*P, C]
    #         global_idx: 更新后的全局块索引
    #         intermediates: 中间结果列表，每个元素形状为 [B, S, P, C]
    #     """
    #     # 确保我们有立体对 (S=2)
    #     if S != 2:
    #         # 对于非立体情况，回退到原始的全局注意力
    #         if tokens.shape != (B, S * P, C):
    #             tokens = tokens.view(B, S, P, C).view(B, S * P, C)
    #         if pos is not None and pos.shape != (B, S * P, 2):
    #             pos = pos.view(B, S, P, 2).view(B, S * P, 2)

    #         intermediates = []
    #         for _ in range(self.aa_block_size):
    #             # tokens: [B, S*P, C] -> [B, S*P, C]
    #             tokens = self.global_blocks[global_idx](tokens, pos=pos)
    #             global_idx += 1
    #             # 中间结果形状: [B, S, P, C]
    #             intermediates.append(tokens.view(B, S, P, C))
    #         return tokens, global_idx, intermediates

    #     # 重塑tokens为 [B, S, P, C] 以进行条带处理
    #     if tokens.shape != (B, S, P, C):
    #         tokens = tokens.view(B, S, P, C)

    #     if pos is not None and pos.shape != (B, S, P, 2):
    #         pos = pos.view(B, S, P, 2)

    #     # 根据实际图像尺寸计算patch布局
    #     H_patches = H // self.patch_size
    #     W_patches = W // self.patch_size

    #     # 处理特殊tokens（相机+寄存器tokens）
    #     # special_tokens: [B, S, special_count, C] 其中special_count = patch_start_idx
    #     special_tokens = tokens[:, :, :self.patch_start_idx, :]  
    #     # patch_tokens: [B, S, H*W, C] 其中H*W = P - patch_start_idx
    #     patch_tokens = tokens[:, :, self.patch_start_idx:, :]    

    #     if pos is not None:
    #         # special_pos: [B, S, special_count, 2]
    #         special_pos = pos[:, :, :self.patch_start_idx, :]
    #         # patch_pos: [B, S, H*W, 2]
    #         patch_pos = pos[:, :, self.patch_start_idx:, :]
    #     else:
    #         special_pos = patch_pos = None

    #     # 将patch tokens重塑为空间布局 [B, S, H_patches, W_patches, C]
    #     # patch_tokens: [B, S, H_patches, W_patches, C]
    #     patch_tokens = patch_tokens.view(B, S, H_patches, W_patches, C)
    #     if patch_pos is not None:
    #         # patch_pos: [B, S, H_patches, W_patches, 2]
    #         patch_pos = patch_pos.view(B, S, H_patches, W_patches, 2)

    #     # 基于条带的注意力参数
    #     # 使用像素高度4来计算patch高度，考虑patch_size
    #     strip_pixel_height = 4
    #     strip_height = max(1, strip_pixel_height // self.patch_size)  # 至少为1个patch
    #     num_strips = (H_patches + strip_height - 1) // strip_height  # 向上取整除法

    #     # 填充高度使其能被strip_height整除，以实现高效的并行处理
    #     padded_H = num_strips * strip_height
    #     pad_h = padded_H - H_patches

    #     if pad_h > 0:
    #         # 填充patch tokens和位置信息
    #         # patch_tokens: [B, S, padded_H, W_patches, C]
    #         patch_tokens = F.pad(patch_tokens, (0, 0, 0, 0, 0, pad_h), mode='constant', value=0)
    #         if patch_pos is not None:
    #             # patch_pos: [B, S, padded_H, W_patches, 2]
    #             patch_pos = F.pad(patch_pos, (0, 0, 0, 0, 0, pad_h), mode='constant', value=0)

    #     intermediates = []

    #     # 处理每个注意力块
    #     for _ in range(self.aa_block_size):
    #         # 直接重塑并重排为条带格式以减少中间变量
    #         # [B, S, padded_H, W_patches, C] -> [B, S, num_strips, strip_height, W_patches, C]
    #         # strip_tokens: [B, S, num_strips, strip_height, W_patches, C]
    #         strip_tokens = patch_tokens[:, :, :padded_H, :, :].contiguous().view(B, S, num_strips, strip_height, W_patches, C)
            
    #         # 重塑以进行并行处理: [B * num_strips, S * strip_height * W_patches, C]
    #         # 合并view和permute操作以减少中间张量
    #         # strip_tokens: [B * num_strips, S * strip_height * W_patches, C]
    #         strip_tokens = strip_tokens.permute(0, 2, 1, 3, 4, 5).reshape(B * num_strips, S * strip_height * W_patches, C)

    #         # 类似地处理位置嵌入
    #         if patch_pos is not None:
    #             # strip_pos: [B, S, num_strips, strip_height, W_patches, 2]
    #             strip_pos = patch_pos[:, :, :padded_H, :, :].contiguous().view(B, S, num_strips, strip_height, W_patches, 2)
    #             # strip_pos: [B * num_strips, S * strip_height * W_patches, 2]
    #             strip_pos = strip_pos.permute(0, 2, 1, 3, 4, 5).reshape(B * num_strips, S * strip_height * W_patches, 2)
    #         else:
    #             strip_pos = None

    #         # 对所有条带并行应用注意力
    #         # strip_tokens: [B * num_strips, S * strip_height * W_patches, C] -> [B * num_strips, S * strip_height * W_patches, C]
    #         strip_tokens = self.global_blocks[global_idx](strip_tokens, pos=strip_pos)

    #         # 重塑回来: [B * num_strips, S * strip_height * W_patches, C] -> [B, S, padded_H, W_patches, C]
    #         # 合并view和permute操作
    #         # strip_tokens: [B, S, padded_H, W_patches, C]
    #         strip_tokens = strip_tokens.view(B, num_strips, S, strip_height, W_patches, C).permute(0, 2, 1, 3, 4, 5).reshape(B, S, padded_H, W_patches, C)

    #         # 如果添加了填充则移除
    #         if pad_h > 0:
    #             # patch_tokens: [B, S, H_patches, W_patches, C]
    #             patch_tokens = strip_tokens[:, :, :H_patches, :, :]
    #         else:
    #             patch_tokens = strip_tokens

    #         # 将空间维度展平回patch序列
    #         # patch_tokens_flat: [B, S, H_patches * W_patches, C]
    #         patch_tokens_flat = patch_tokens.reshape(B, S, H_patches * W_patches, C)

    #         # 将特殊tokens与patch tokens连接
    #         # tokens: [B, S, P, C]
    #         tokens = torch.cat([special_tokens, patch_tokens_flat], dim=2)  

    #         global_idx += 1
    #         # 中间结果形状: [B, S, P, C]
    #         intermediates.append(tokens)

    #     # 返回处理结果
    #     # tokens.view(B, S * P, C): [B, S*P, C]
    #     # global_idx: 更新后的索引
    #     # intermediates: List[torch.Tensor]，每个元素形状为 [B, S, P, C]
    #     return tokens.view(B, S * P, C), global_idx, intermediates

    def _process_global_attention(self, tokens, B, S, P, C, global_idx, pos=None):
        """
        Process global attention blocks. We keep tokens in shape (B, S*P, C).
        """
        if tokens.shape != (B, S * P, C):
            tokens = tokens.view(B, S, P, C).view(B, S * P, C)

        if pos is not None and pos.shape != (B, S * P, 2):
            pos = pos.view(B, S, P, 2).view(B, S * P, 2)

        intermediates = []

        # by default, self.aa_block_size=1, which processes one block at a time
        for _ in range(self.aa_block_size):
            tokens = self.global_blocks[global_idx](tokens, pos=pos)
            global_idx += 1
            intermediates.append(tokens.view(B, S, P, C))

        return tokens, global_idx, intermediates


def slice_expand_and_flatten(token_tensor, B, S):
    """
    Processes specialized tokens with shape (1, 2, X, C) for multi-frame processing:
    1) Uses the first position (index=0) for the first frame only
    2) Uses the second position (index=1) for all remaining frames (S-1 frames)
    3) Expands both to match batch size B
    4) Concatenates to form (B, S, X, C) where each sequence has 1 first-position token
       followed by (S-1) second-position tokens
    5) Flattens to (B*S, X, C) for processing

    Args:
        token_tensor: 输入张量 [1, 2, X, C]
        B: batch size
        S: sequence length

    Returns:
        torch.Tensor: Processed tokens with shape (B*S, X, C)
    """

    # Slice out the "query" tokens => shape (1, 1, X, C)
    # query: [B, 1, X, C]
    query = token_tensor[:, 0:1, ...].expand(B, 1, *token_tensor.shape[2:])
    # Slice out the "other" tokens => shape (1, S-1, X, C)
    # others: [B, S-1, X, C]
    others = token_tensor[:, 1:, ...].expand(B, S - 1, *token_tensor.shape[2:])
    # Concatenate => shape (B, S, X, C)
    # combined: [B, S, X, C]
    combined = torch.cat([query, others], dim=1)

    # Finally flatten => shape (B*S, X, C)
    # combined: [B*S, X, C]
    combined = combined.view(B * S, *combined.shape[2:])
    return combined