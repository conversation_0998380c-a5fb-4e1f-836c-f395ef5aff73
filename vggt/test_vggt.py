#!/usr/bin/env python3
"""
Test script for VGGT model.
This script tests if the VGGT model can run properly.
"""

import torch
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.vggt import VGGT


def test_vggt_model():
    """Test VGGT model instantiation and forward pass."""
    print("Testing VGGT model...")
    
    # Check if CUDA is available
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Create model
    model = VGGT(img_size=518, patch_size=14, embed_dim=1024)
    model = model.to(device)
    model.eval()
    
    print(f"Model created successfully with img_size=518, patch_size=14, embed_dim=1024")
    
    # Test 1: Forward pass without query points
    print("\nTest 1: Forward pass without query points")
    try:
        # Create dummy input images [S, 3, H, W] format
        dummy_images = torch.randn(2, 3, 518, 518).to(device)  # 2 frames, 3 channels, 518x518
        print(f"Input images shape: {dummy_images.shape}")
        
        with torch.no_grad():
            output = model(dummy_images)
            
        print("Forward pass without query points successful!")
        print("Output keys:", list(output.keys()))
        
        # Print shapes of important outputs
        if "pose_enc" in output:
            print(f"Pose encoding shape: {output['pose_enc'].shape}")
        if "depth" in output:
            print(f"Depth shape: {output['depth'].shape}")
        if "depth_conf" in output:
            print(f"Depth confidence shape: {output['depth_conf'].shape}")
        if "world_points" in output:
            print(f"World points shape: {output['world_points'].shape}")
        if "world_points_conf" in output:
            print(f"World points confidence shape: {output['world_points_conf'].shape}")
            
    except Exception as e:
        print(f"Error in test 1: {e}")
        return False
    
    # Test 2: Forward pass with query points
    print("\nTest 2: Forward pass with query points")
    try:
        # Create dummy input images with batch dimension [B, S, 3, H, W]
        dummy_images_batch = torch.randn(1, 2, 3, 518, 518).to(device)  # 1 batch, 2 frames, 3 channels, 518x518
        print(f"Input images shape: {dummy_images_batch.shape}")
        
        # Create dummy query points [B, N, 2]
        dummy_query_points = torch.randn(1, 10, 2).to(device)  # 1 batch, 10 points
        print(f"Query points shape: {dummy_query_points.shape}")
        
        with torch.no_grad():
            output = model(dummy_images_batch, query_points=dummy_query_points)
            
        print("Forward pass with query points successful!")
        print("Output keys:", list(output.keys()))
        
        # Print shapes of tracking outputs
        if "track" in output:
            print(f"Track shape: {output['track'].shape}")
        if "vis" in output:
            print(f"Visibility shape: {output['vis'].shape}")
        if "conf" in output:
            print(f"Confidence shape: {output['conf'].shape}")
            
    except Exception as e:
        print(f"Error in test 2: {e}")
        return False
    
    # Test 3: Different input format (without batch dimension)
    print("\nTest 3: Forward pass with inputs without batch dimension")
    try:
        # Create dummy input images [S, 3, H, W] format (no batch dimension)
        dummy_images_no_batch = torch.randn(2, 3, 518, 518).to(device)  # 2 frames, 3 channels, 518x518
        print(f"Input images shape: {dummy_images_no_batch.shape}")
        
        # Create dummy query points [N, 2] format (no batch dimension)
        dummy_query_points_no_batch = torch.randn(10, 2).to(device)  # 10 points
        print(f"Query points shape: {dummy_query_points_no_batch.shape}")
        
        with torch.no_grad():
            output = model(dummy_images_no_batch, query_points=dummy_query_points_no_batch)
            
        print("Forward pass with inputs without batch dimension successful!")
        print("Output keys:", list(output.keys()))
        
    except Exception as e:
        print(f"Error in test 3: {e}")
        return False
    
    print("\nAll tests passed! VGGT model is working correctly.")
    return True


if __name__ == "__main__":
    test_vggt_model()