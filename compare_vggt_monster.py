#!/usr/bin/env python3
"""
对比VGGT和Monster Plus的特征图生成
验证两者是否能生成相同尺寸的多尺度特征图
"""

import torch
import torch.nn.functional as F
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'vggt'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from vggt.models.vggt import VGGT

def simulate_monster_plus_processing():
    """模拟Monster Plus的infer_mono处理过程"""
    print("🔍 模拟Monster Plus的infer_mono处理过程:")
    
    # 原始输入尺寸
    height_ori, width_ori = 512, 512
    print(f"  原始输入尺寸: {height_ori}x{width_ori}")
    
    # Monster Plus的resize操作
    resize_factor = 14 / 16
    resize_h = int(height_ori * resize_factor)
    resize_w = int(width_ori * resize_factor)
    print(f"  resize后尺寸: {resize_h}x{resize_w} (factor: {resize_factor})")
    
    # patch计算
    patch_h, patch_w = resize_h // 14, resize_w // 14
    print(f"  patch尺寸: {patch_h}x{patch_w}")
    
    # 模拟feat_decoder的输出尺寸
    # 根据Monster Plus的resize_layers配置推算
    print(f"\n  feat_decoder预期输出尺寸:")
    
    # Layer 0: 上采样4倍 -> patch_size * 4
    feat_0_h, feat_0_w = patch_h * 4, patch_w * 4
    print(f"    Layer 0 (1/4): {feat_0_h}x{feat_0_w}")
    
    # Layer 1: 上采样2倍 -> patch_size * 2  
    feat_1_h, feat_1_w = patch_h * 2, patch_w * 2
    print(f"    Layer 1 (1/8): {feat_1_h}x{feat_1_w}")
    
    # Layer 2: 保持原尺寸 -> patch_size
    feat_2_h, feat_2_w = patch_h, patch_w
    print(f"    Layer 2 (1/16): {feat_2_h}x{feat_2_w}")
    
    # Layer 3: 下采样2倍 -> patch_size / 2
    feat_3_h, feat_3_w = patch_h // 2, patch_w // 2
    print(f"    Layer 3 (1/32): {feat_3_h}x{feat_3_w}")
    
    return {
        "original_size": (height_ori, width_ori),
        "resize_size": (resize_h, resize_w),
        "patch_size": (patch_h, patch_w),
        "feature_sizes": {
            "feat_1_4": (feat_0_h, feat_0_w),
            "feat_1_8": (feat_1_h, feat_1_w),
            "feat_1_16": (feat_2_h, feat_2_w),
            "feat_1_32": (feat_3_h, feat_3_w),
        }
    }

def test_vggt_vs_monster_plus():
    """对比VGGT和Monster Plus的特征图尺寸"""
    print("\n🚀 开始对比测试...")
    
    # 模拟Monster Plus处理
    monster_info = simulate_monster_plus_processing()
    
    # 测试VGGT
    print(f"\n🔧 测试VGGT模型:")
    model = VGGT(img_size=512, patch_size=14, embed_dim=1024)
    model.eval()
    
    # 创建测试输入
    batch_size = 1
    sequence_length = 2
    height, width = 512, 512
    images = torch.randn(batch_size, sequence_length, 3, height, width)
    
    print(f"  输入尺寸: {images.shape}")
    
    # VGGT前向传播
    with torch.no_grad():
        predictions = model(images)
    
    print(f"  输出特征图: {list(predictions.keys())}")
    
    # 对比特征图尺寸
    print(f"\n📊 特征图尺寸对比:")
    print(f"{'特征图':<12} {'Monster Plus':<15} {'VGGT':<15} {'匹配':<8}")
    print("-" * 55)
    
    all_match = True
    for feat_name in ["feat_1_4", "feat_1_8", "feat_1_16", "feat_1_32"]:
        # Monster Plus预期尺寸
        monster_size = monster_info["feature_sizes"][feat_name]
        
        # VGGT实际尺寸
        if feat_name in predictions:
            vggt_feat = predictions[feat_name]
            vggt_size = (vggt_feat.shape[-2], vggt_feat.shape[-1])
            
            # 检查是否匹配
            match = monster_size == vggt_size
            match_str = "✅" if match else "❌"
            
            print(f"{feat_name:<12} {str(monster_size):<15} {str(vggt_size):<15} {match_str:<8}")
            
            if not match:
                all_match = False
        else:
            print(f"{feat_name:<12} {str(monster_size):<15} {'缺失':<15} {'❌':<8}")
            all_match = False
    
    print("-" * 55)
    
    if all_match:
        print("🎉 完美匹配! VGGT生成的特征图尺寸与Monster Plus完全一致!")
    else:
        print("⚠️  存在尺寸差异，需要进一步调整...")
    
    # 详细分析
    print(f"\n📋 详细分析:")
    print(f"  Monster Plus处理流程:")
    print(f"    1. 输入 512x512 -> resize到 {monster_info['resize_size']}")
    print(f"    2. 计算patch: {monster_info['patch_size']}")
    print(f"    3. feat_decoder生成4个尺度特征图")
    print(f"    4. 最终特征图对应原图的1/4, 1/8, 1/16, 1/32")
    
    print(f"\n  VGGT处理流程:")
    print(f"    1. 输入 512x512 -> 内部resize到 {int(512*14/16)}x{int(512*14/16)}")
    print(f"    2. Aggregator提取多层特征")
    print(f"    3. MonsterStyleDPTHead生成多尺度特征金字塔")
    print(f"    4. 输出1/4, 1/8, 1/16, 1/32尺度的特征图")
    
    return all_match

def test_different_input_sizes():
    """测试不同输入尺寸下的特征图生成"""
    print(f"\n🔄 测试不同输入尺寸:")
    
    model = VGGT(img_size=512, patch_size=14, embed_dim=1024)
    model.eval()
    
    test_sizes = [(256, 256), (512, 512), (768, 768), (1024, 1024)]
    
    for h, w in test_sizes:
        print(f"\n  测试尺寸: {h}x{w}")
        
        images = torch.randn(1, 2, 3, h, w)
        
        with torch.no_grad():
            try:
                predictions = model(images)
                
                print(f"    特征图尺寸:")
                for feat_name in ["feat_1_4", "feat_1_8", "feat_1_16", "feat_1_32"]:
                    if feat_name in predictions:
                        feat = predictions[feat_name]
                        feat_h, feat_w = feat.shape[-2], feat.shape[-1]
                        scale = int(feat_name.split('_')[1])
                        expected_h, expected_w = h // scale, w // scale
                        match = feat_h == expected_h and feat_w == expected_w
                        match_str = "✅" if match else "❌"
                        print(f"      {feat_name}: {feat_h}x{feat_w} (期望: {expected_h}x{expected_w}) {match_str}")
                        
            except Exception as e:
                print(f"    ❌ 处理失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("VGGT vs Monster Plus 特征图对比测试")
    print("=" * 60)
    
    # 主要对比测试
    success = test_vggt_vs_monster_plus()
    
    # 测试不同输入尺寸
    test_different_input_sizes()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 对比测试通过! VGGT已成功实现Monster Plus风格的多尺度特征生成!")
        print("现在你可以在立体匹配任务中使用VGGT生成的1/4, 1/8, 1/16, 1/32特征图了!")
    else:
        print("❌ 对比测试失败! 需要进一步调整VGGT的实现...")
    print("=" * 60)
