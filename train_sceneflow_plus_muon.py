import os
import hydra
import torch
from tqdm import tqdm
import torch.optim as optim
from core.utils.utils import InputPadder
from core.monste_plus import Monster  
from omegaconf import OmegaConf
import torch.nn.functional as F
from accelerate import Accelerator
import core.stereo_datasets as datasets
from accelerate.utils import set_seed
from accelerate.logging import get_logger
from accelerate import DataLoaderConfiguration
from accelerate.utils import DistributedDataParallelKwargs

import matplotlib
import matplotlib.pyplot as plt
import numpy as np
import wandb
from pathlib import Path

# 🔥 导入 Muon 优化器
try:
    from core.muon_optimizer import MuonWithAuxAdam
    MUON_AVAILABLE = True
    print("✅ Muon 优化器导入成功")
except ImportError:
    print("❌ Muon 优化器导入失败，将回退到 AdamW")
    MUON_AVAILABLE = False

def gray_2_colormap_np(img, cmap = 'rainbow', max = None):
    """将灰度图转换为彩色图"""
    img = img.cpu().detach().numpy().squeeze()
    assert img.ndim == 2
    img[img<0] = 0
    mask_invalid = img < 1e-10
    if max == None:
        img = img / (img.max() + 1e-8)
    else:
        img = img/(max + 1e-8)

    norm = matplotlib.colors.Normalize(vmin=0, vmax=1.1)
    cmap_m = matplotlib.cm.get_cmap(cmap)
    map = matplotlib.cm.ScalarMappable(norm=norm, cmap=cmap_m)
    colormap = (map.to_rgba(img)[:,:,:3]*255).astype(np.uint8)
    colormap[mask_invalid] = 0

    return colormap

def sequence_loss(disp_preds, disp_init_pred, disp_gt, valid, loss_gamma=0.9, max_disp=192):
    """
    简化的视差损失函数（删除置信度部分）
    
    Loss = Σ [ γ^(n-i-1) * |D̂ - D| ]
    
    其中：
    - D̂: 预测视差
    - D: 真实视差
    - γ: 损失权重衰减因子
    """
    n_predictions = len(disp_preds)
    assert n_predictions >= 1
    
    disp_loss = 0.0
    
    # 有效区域mask
    mag = torch.sum(disp_gt**2, dim=1).sqrt()
    valid = ((valid >= 0.5) & (mag < max_disp)).unsqueeze(1)
    assert valid.shape == disp_gt.shape, [valid.shape, disp_gt.shape]
    
    # 为初始预测创建有效区域mask，确保维度匹配
    mag_init = torch.sum(disp_init_pred**2, dim=1).sqrt()  # [B, H, W]
    init_valid = ((valid.squeeze(1) >= 0.5) & (mag_init < 768)).unsqueeze(1)  # [B, 1, H, W]
    disp_loss += 1.0 * F.smooth_l1_loss(disp_init_pred[init_valid], disp_gt[init_valid], reduction='mean')
    
    # 对每个预测计算视差损失
    for i in range(n_predictions):
        if n_predictions > 1:
            adjusted_loss_gamma = loss_gamma**(15/(n_predictions - 1))
            i_weight = adjusted_loss_gamma**(n_predictions - i - 1)
        else:
            i_weight = 1.0
        
        # 获取当前预测
        disp_pred = disp_preds[i]
        
        # 视差误差
        disp_error = (disp_pred - disp_gt).abs()
        
        # 只在有效区域计算损失
        valid_mask = valid.bool() & ~torch.isnan(disp_error)
        
        if valid_mask.sum() > 0:
            disp_loss += i_weight * disp_error[valid_mask].mean()
    
    # 计算评估指标
    epe = torch.sum((disp_preds[-1] - disp_gt)**2, dim=1).sqrt()
    epe = epe.view(-1)[valid.view(-1)]
    
    if valid.bool().sum() == 0:
        epe = torch.Tensor([0.0]).cuda()

    metrics = {
        'train/epe': epe.mean(),
        'train/1px': (epe < 1).float().mean(),
        'train/3px': (epe < 3).float().mean(),
        'train/5px': (epe < 5).float().mean(),
        'train/disp_loss': disp_loss,
    }
    
    return disp_loss, metrics

def fetch_optimizer_muon(args, model):
    """
    🔥 Muon 混合优化器策略（基于官方实现）
    
    参数分组：
    - 2D+隐藏层权重: 使用 Muon 优化器
    - 1D参数 + embedding/输出层: 使用 AdamW 优化器
    
    根据 Muon 论文建议：
    - Muon 使用自适应学习率调整
    - AdamW 保持标准学习率
    """
    if not MUON_AVAILABLE:
        print("⚠️  Muon 不可用，回退到标准 AdamW 优化器")
        return fetch_optimizer_adamw(args, model)
    
    # 🔥 按照官方 Muon 实现的参数分组策略
    muon_params = []      # 2D+ 参数用 Muon（隐藏层）
    adamw_params = []     # 其他参数用 AdamW
    
    # 统计变量
    muon_param_count = 0
    adamw_param_count = 0
    frozen_param_count = 0
    
    print(f"\n🔍 Muon 优化器参数分组分析:")
    
    for name, param in model.named_parameters():
        param_count = param.numel()
        
        if not param.requires_grad:
            frozen_param_count += param_count
            continue
            
        # 按照官方 Muon 建议：
        # - 2D+ 参数且不是 embedding/head 层 → Muon
        # - 其他参数 → AdamW
        if (param.ndim >= 2 and 
            not any(x in name.lower() for x in ['embed', 'head', 'classifier', 'mono_encoder', 'mono_decoder'])):
            muon_params.append(param)
            muon_param_count += param_count
        else:
            adamw_params.append(param)
            adamw_param_count += param_count
    
    total_trainable = muon_param_count + adamw_param_count
    total_params = total_trainable + frozen_param_count
    
    print(f"   🎯 Muon 参数: {muon_param_count:,} ({muon_param_count/total_trainable*100:.1f}% of trainable)")
    print(f"   ⚙️  AdamW 参数: {adamw_param_count:,} ({adamw_param_count/total_trainable*100:.1f}% of trainable)")
    print(f"   ❄️  冻结参数: {frozen_param_count:,} ({frozen_param_count/total_params*100:.1f}% of total)")
    print(f"   📊 总参数: {total_params:,}")
    
    muon_lr = args.lr         
    adamw_lr = args.lr     
    
    
    # 创建 Muon 混合优化器（使用官方 API）
    optimizer = MuonWithAuxAdam(
        lr=muon_lr,
        wd=args.wdecay,
        muon_params=muon_params,
        adamw_params=adamw_params,
        momentum=0.95,
        nesterov=True,
        ns_steps=5,
        adamw_betas=(0.9, 0.95),
        adamw_eps=1e-8,
    )
    
    print(f"✅ Muon 混合优化器创建成功！")
    print(f"   - {len(muon_params)} 个权重使用 Muon")
    print(f"   - {len(adamw_params)} 个参数使用 AdamW")
    
    # 标准学习率调度器
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, 
        muon_lr,  # 使用 Muon 学习率作为主要学习率
        args.total_step + 100,
        pct_start=0.01, 
        cycle_momentum=False,
        anneal_strategy='linear'
    )
    
    return optimizer, scheduler

def fetch_optimizer_adamw(args, model):
    """
    回退的 AdamW 优化器（当 Muon 不可用时）
    """
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    trainable_count = sum(p.numel() for p in trainable_params)
    total_params = sum(p.numel() for p in model.parameters())
    frozen_count = total_params - trainable_count
    
    print(f"\n🎯 AdamW 优化器参数设置:")
    print(f"   ✅ 可训练参数: {trainable_count:,} ({trainable_count/total_params*100:.1f}%)")
    print(f"   ❄️  冻结参数: {frozen_count:,} ({frozen_count/total_params*100:.1f}%)")
    print(f"   📊 总参数: {total_params:,}")
    print(f"   🎪 学习率: {args.lr:.2e}")
    
    optimizer = optim.AdamW(trainable_params, lr=args.lr, weight_decay=args.wdecay, eps=1e-8)

    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, 
        args.lr,
        args.total_step + 100,
        pct_start=0.01, 
        cycle_momentum=False, 
        anneal_strategy='linear'
    )

    return optimizer, scheduler

def fetch_optimizer(args, model):
    """
    优化器选择函数：优先使用 Muon，不可用时回退到 AdamW
    """
    if MUON_AVAILABLE:
        return fetch_optimizer_muon(args, model)
    else:
        return fetch_optimizer_adamw(args, model)

@hydra.main(version_base=None, config_path='config', config_name='train_sceneflow')
def main(cfg):
    set_seed(cfg.seed)
    logger = get_logger(__name__)
    Path(cfg.save_path).mkdir(exist_ok=True, parents=True)
    kwargs = DistributedDataParallelKwargs(find_unused_parameters=True)
    
    # 检查是否为测试模式（total_step < 100则认为是测试）
    is_test_mode = getattr(cfg, 'total_step', 50000) < 100
    
    if is_test_mode:
        # 测试模式：禁用wandb
        accelerator = Accelerator(
            mixed_precision='bf16', 
            dataloader_config=DataLoaderConfiguration(use_seedable_sampler=True), 
            kwargs_handlers=[kwargs], 
            step_scheduler_with_optimizer=False
        )
    else:
        # 正常训练模式：启用wandb
        accelerator = Accelerator(
            mixed_precision='bf16', 
            dataloader_config=DataLoaderConfiguration(use_seedable_sampler=True), 
            log_with='wandb', 
            kwargs_handlers=[kwargs], 
            step_scheduler_with_optimizer=False
        )
    
    # 🔥 Muon 优化器信息打印
    print(f"\n🚀 Monster Plus + Muon 优化器训练")
    print(f"🔥 优化器状态: {'Muon混合优化器' if MUON_AVAILABLE else 'AdamW优化器(回退)'}")
    print(f"🎮 训练设备: {accelerator.num_processes} GPU(s)")
    print(f"📏 图像尺寸: {cfg.image_size}")
    print(f"🎯 最大视差: {cfg.max_disp}")
    print(f"⏱️  总训练步数: {cfg.total_step}")
    
    # 只在非测试模式下初始化wandb
    if not is_test_mode:
        # 设置wandb为离线模式
        os.environ["WANDB_MODE"] = "offline"
        
        # 修复wandb配置冲突
        wandb_config = getattr(cfg, 'wandb', {})
        if isinstance(wandb_config, dict):
            wandb_config = dict(wandb_config)  # 复制配置
            # 移除可能冲突的project参数
            wandb_config.pop('project', None)
        
        # 修改项目名以区分 Muon 实验
        project_name = cfg.project_name + "_muon" if MUON_AVAILABLE else cfg.project_name + "_adamw"
        
        accelerator.init_trackers(
            project_name=project_name,
            config=OmegaConf.to_container(cfg, resolve=True), 
            init_kwargs={'wandb': wandb_config}
        )
    else:
        print("🧪 测试模式：跳过wandb初始化")

    # 数据集
    train_dataset = datasets.fetch_dataloader(cfg)
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=cfg.batch_size//cfg.num_gpu,
        pin_memory=False,  # 🔥 改为False减少内存占用
        shuffle=True, 
        num_workers=2,     # 🔥 改为2提高效率
        drop_last=True
    )

    # 创建Monster Plus模型
    model = Monster(cfg)
    
    # 加载预训练权重
    if not cfg.restore_ckpt.endswith("None"):
        assert cfg.restore_ckpt.endswith(".pth")
        print(f"Loading checkpoint from {cfg.restore_ckpt}")
        assert os.path.exists(cfg.restore_ckpt)
        checkpoint = torch.load(cfg.restore_ckpt, map_location='cpu')
        ckpt = dict()
        if 'state_dict' in checkpoint.keys():
            checkpoint = checkpoint['state_dict']
        for key in checkpoint:
            ckpt[key.replace('module.', '')] = checkpoint[key]

        # 🔥 智能权重加载：只加载存在且维度匹配的权重
        model_state = model.state_dict()
        model_keys = set(model_state.keys())
        ckpt_keys = set(ckpt.keys())
        
        print(f"\n🔍 权重加载详细信息:")
        print(f"📊 模型中的权重数量: {len(model_keys)}")
        print(f"📊 checkpoint中的权重数量: {len(ckpt_keys)}")
        
        # 预处理：检查维度匹配
        matched_keys = []
        shape_mismatch_keys = []
        missing_keys = []
        extra_keys = []
        
        # 检查每个模型权重
        for key in model_keys:
            if key in ckpt_keys:
                model_shape = model_state[key].shape
                ckpt_shape = ckpt[key].shape
                
                if model_shape == ckpt_shape:
                    matched_keys.append(key)
                else:
                    shape_mismatch_keys.append((key, model_shape, ckpt_shape))
            else:
                missing_keys.append(key)
        
        # 检查checkpoint中多余的权重
        for key in ckpt_keys:
            if key not in model_keys:
                extra_keys.append(key)
        
        # 创建过滤后的checkpoint（只包含维度匹配的权重）
        filtered_ckpt = {}
        for key in matched_keys:
            filtered_ckpt[key] = ckpt[key]
        
        print(f"✅ 维度匹配的权重: {len(matched_keys)}")
        print(f"⚠️  维度不匹配的权重: {len(shape_mismatch_keys)}")
        print(f"❌ 模型中缺失的权重: {len(missing_keys)}")
        print(f"ℹ️  checkpoint中多余的权重: {len(extra_keys)}")
        
        # 显示维度不匹配的详细信息
        if shape_mismatch_keys:
            print(f"\n⚠️  维度不匹配的权重详情:")
            for key, model_shape, ckpt_shape in sorted(shape_mismatch_keys):
                print(f"   - {key}: 模型{model_shape} vs checkpoint{ckpt_shape}")
        
        # 显示缺失权重的前几个（避免输出过长）
        if missing_keys:
            print(f"\n❌ 模型中缺失的权重 (前10个):")
            for key in sorted(missing_keys)[:10]:
                print(f"   - {key}")
            if len(missing_keys) > 10:
                print(f"   ... 还有 {len(missing_keys)-10} 个权重")
        
        # 按模块分类显示加载情况
        print(f"\n📋 按模块分类的权重加载情况:")
        modules = {}
        for key in model_keys:
            module_name = key.split('.')[0]
            if module_name not in modules:
                modules[module_name] = {'total': 0, 'loaded': 0, 'shape_mismatch': 0}
            modules[module_name]['total'] += 1
            if key in matched_keys:
                modules[module_name]['loaded'] += 1
            elif any(key == mkey for mkey, _, _ in shape_mismatch_keys):
                modules[module_name]['shape_mismatch'] += 1
        
        for module_name, stats in sorted(modules.items()):
            total = stats['total']
            loaded = stats['loaded']
            mismatch = stats['shape_mismatch']
            missing = total - loaded - mismatch
            
            loaded_ratio = loaded / total * 100
            status = "✅" if loaded_ratio == 100 else "⚠️" if loaded_ratio > 0 else "❌"
            
            info_parts = [f"{loaded}/{total}"]
            if mismatch > 0:
                info_parts.append(f"{mismatch}维度不匹配")
            if missing > 0:
                info_parts.append(f"{missing}缺失")
                
            print(f"   {status} {module_name}: {', '.join(info_parts)} ({loaded_ratio:.1f}%)")

        # 🔥 安全加载：只加载维度匹配的权重
        missing_keys, unexpected_keys = model.load_state_dict(filtered_ckpt, strict=False)
        
        print(f"\n🎯 智能权重加载结果:")
        print(f"✅ 成功加载 {len(matched_keys)} 个维度匹配的权重")
        if shape_mismatch_keys:
            print(f"⚠️  跳过 {len(shape_mismatch_keys)} 个维度不匹配的权重")
        if missing_keys:
            print(f"❌ 缺失 {len(missing_keys)} 个权重（使用随机初始化）")
        if extra_keys:
            print(f"ℹ️  忽略 {len(extra_keys)} 个checkpoint中多余的权重")
        
        # 计算加载覆盖率
        total_model_params = len(model_keys)
        loaded_params = len(matched_keys)
        coverage_rate = loaded_params / total_model_params * 100
        
        print(f"📊 权重加载覆盖率: {loaded_params}/{total_model_params} ({coverage_rate:.1f}%)")
        
        if coverage_rate >= 90:
            print(f"🎉 权重加载完成！覆盖率优秀 ({coverage_rate:.1f}%)")
        elif coverage_rate >= 70:
            print(f"✅ 权重加载完成！覆盖率良好 ({coverage_rate:.1f}%)")
        elif coverage_rate >= 50:
            print(f"⚠️  权重加载完成！覆盖率一般 ({coverage_rate:.1f}%)")
        else:
            print(f"❌ 权重加载完成！覆盖率较低 ({coverage_rate:.1f}%)，建议检查checkpoint匹配性")
        
        print(f"📂 Checkpoint: {cfg.restore_ckpt}")
        del ckpt, checkpoint, filtered_ckpt
    
    # 🔥 创建 Muon 混合优化器
    optimizer, lr_scheduler = fetch_optimizer(cfg, model)
    
    train_loader, model, optimizer, lr_scheduler = accelerator.prepare(
        train_loader, model, optimizer, lr_scheduler
    )
    model.to(accelerator.device)

    total_step = 0
    should_keep_training = True
    
    while should_keep_training:
        active_train_loader = train_loader

        model.train()
        # 兼容单卡和多卡训练
        if hasattr(model, 'module'):
            model.module.freeze_bn()
        else:
            model.freeze_bn()
        
        for data in tqdm(active_train_loader, dynamic_ncols=True, disable=not accelerator.is_main_process):
            _, left, right, disp_gt, valid = [x for x in data]
            
            with accelerator.autocast():
                disp_init_pred, disp_preds = model(left, right, iters=cfg.train_iters)
            
            # 使用简化的视差损失函数
            loss, metrics = sequence_loss(
                disp_preds, disp_init_pred, disp_gt, valid, 
                max_disp=cfg.max_disp
            )
            
            accelerator.backward(loss)
            accelerator.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            lr_scheduler.step()
            optimizer.zero_grad()

            total_step += 1
            loss = accelerator.reduce(loss.detach(), reduction='mean')
            metrics = accelerator.reduce(metrics, reduction='mean')
            
            # 记录训练指标
            if not is_test_mode:
                # 🔥 记录 Muon 混合优化器的学习率
                if MUON_AVAILABLE:
                    lr_dict = {
                        'train/loss': loss,
                        'train/muon_lr': optimizer.param_groups[0]['lr'],  # Muon 学习率（主要）
                    }
                else:
                    lr_dict = {
                        'train/loss': loss,
                        'train/lr': optimizer.param_groups[0]['lr']
                    }
                accelerator.log(lr_dict, total_step)
                accelerator.log(metrics, total_step)

            # 每100步打印训练指标
            if total_step % 100 == 0 and accelerator.is_main_process:
                print(f"\n=== Step {total_step} Training Metrics ===")
                print(f"Optimizer: {'Muon混合' if MUON_AVAILABLE else 'AdamW'}")
                print(f"Disp Loss: {loss.item():.6f}")
                
                if MUON_AVAILABLE:
                    print(f"Muon LR: {optimizer.param_groups[0]['lr']:.4f}")
                else:
                    print(f"Learning Rate: {optimizer.param_groups[0]['lr']:.2e}")
                    
                print(f"EPE: {metrics['train/epe'].item():.4f}")
                print(f"1px: {metrics['train/1px'].item():.4f}")
                print(f"3px: {metrics['train/3px'].item():.4f}")
                print(f"5px: {metrics['train/5px'].item():.4f}")
                print("=" * 40)

            # 保存模型
            if (total_step > 0) and (total_step % cfg.save_frequency == 0):
                if accelerator.is_main_process:
                    # 🔥 保存文件名包含优化器信息
                    optimizer_suffix = "muon" if MUON_AVAILABLE else "adamw"
                    save_path = Path(cfg.save_path + f'/{total_step}_{optimizer_suffix}.pth')
                    model_save = accelerator.unwrap_model(model)
                    torch.save(model_save.state_dict(), save_path)
                    print(f"Model saved to {save_path}")
                    del model_save

            if total_step == cfg.total_step:
                should_keep_training = False
                break

    # 保存最终模型
    if accelerator.is_main_process:
        optimizer_suffix = "muon" if MUON_AVAILABLE else "adamw"
        save_path = Path(cfg.save_path + f'/final_{optimizer_suffix}.pth')
        model_save = accelerator.unwrap_model(model)
        torch.save(model_save.state_dict(), save_path)
        print(f"Final model saved to {save_path}")
        del model_save
    
    if not is_test_mode:
        accelerator.end_training()

if __name__ == '__main__':
    main() 